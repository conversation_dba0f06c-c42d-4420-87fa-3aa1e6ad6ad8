
from __future__ import annotations
import async<PERSON>
import json
from typing import Optional, List, Union, Any, Dict, cast
from contextlib import AsyncExitStack

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from anthropic import Anthropic
from anthropic.types import MessageParam, ToolParam

from dotenv import load_dotenv

load_dotenv()	# Load environment variables from .env file

class MCPClient:
	def __init__(self):
		self.session: Optional[ClientSession] = None
		self.exit_stack = AsyncExitStack()
		self.anthropic = Anthropic()

	async def connect(self, server_path: str):
		"""Connect to MCP Server

		Args:
			server_path: Path to the server script (.py or .js)
		"""
		is_python = server_path.endswith('.py')
		is_node = server_path.endswith('.js')

		if not(is_python or is_node):
			raise ValueError('Server script must be a .py or .js file')
		
		command = "python" if is_python else "node"
		server_params = StdioServerParameters(
			command=command,
			args=[server_path],
			env=None
		)

		stdio_transport = await self.exit_stack.enter_async_context(
			stdio_client(server_params)
		)
		self.stdio, self.write = stdio_transport
		self.session = await self.exit_stack.enter_async_context(
			ClientSession(self.stdio, self.write)
		)

		await self.session.initialize()

		# List available tools
		toolsData = await self.session.list_tools()
		tools = toolsData.tools
		print(f"\nAvailable tools: {[tool.name for tool in tools]}")
	
	async def query(self, query: str) -> str:
		"""Process query using Claude and available tools"""
		messages: List[MessageParam] = [
			{
				"role": "user",
				"content": query
			}
		]

		if not self.session:
			raise RuntimeError("Not connected to MCP server. Call connect() first.")

		toolData = await self.session.list_tools()
		available_tools: List[ToolParam] = []
		for tool in toolData.tools:
			tool_param: ToolParam = {
				"name": tool.name,
				"description": tool.description or "",
				"input_schema": tool.inputSchema
			}
			available_tools.append(tool_param)

		# Initialize Claude API call
		response = self.anthropic.messages.create(
			model="claude-3-5-sonnet-20241022",
			max_tokens=1000,
			messages=messages,
			tools=available_tools
		)

		final_text = []

		agent_messages = []
		
		# Return the response content as a string
		if response.content:
			for content in response.content:
				if content.type == 'text':
					final_text.append(content.text)
					agent_messages.append(content)
				elif content.type == 'tool_use':
					tool_name = content.name
					# Convert the input object to a dictionary
					# The Anthropic API returns content.input as a dict-like object
					tool_args: Dict[str, Any] = cast(Dict[str, Any], content.input) if content.input else {}

					result = await self.session.call_tool(tool_name, tool_args)
					final_text.append(f"[Calling tool {tool_name} with arguments {json.dumps(tool_args)}]")

					agent_messages.append(content)
					messages.append({
						"role": "assistant",
						"content": agent_messages
					})
					messages.append({
						"role": "user",
						"content": [
							{
								"type": "tool_result",
								"tool_use_id": content.id,
								"content":result.content
							}
						]
					})
